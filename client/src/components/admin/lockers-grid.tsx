import { useState, useEffect, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Plus, <PERSON><PERSON><PERSON>zon<PERSON>, Trash2, <PERSON><PERSON><PERSON>riangle, ArrowUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { useWebSocket } from "@/hooks/useWebSocket";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Locker } from "@shared/schema";
import { AddLockerDialog } from "./add-locker-dialog";

export function LockersGrid() {
  const [addLockerOpen, setAddLockerOpen] = useState(false);
  const [selectedLocker, setSelectedLocker] = useState<Locker | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [lockersPerPage] = useState(12); // Show 12 lockers per page
  const [sortBy, setSortBy] = useState<'code' | 'status' | 'size' | 'created'>('code');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const { lastMessage } = useWebSocket();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: lockers = [] } = useQuery({
    queryKey: ["/api/lockers"],
    refetchInterval: 5000, // Refresh every 5 seconds for real-time data
  });

  // Sort and filter lockers
  const sortedLockers = useMemo(() => {
    const sorted = [...(lockers as Locker[])].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortBy) {
        case 'code':
          aValue = a.code || '';
          bValue = b.code || '';
          break;
        case 'status':
          aValue = a.status || '';
          bValue = b.status || '';
          break;
        case 'size':
          aValue = a.size || '';
          bValue = b.size || '';
          break;
        case 'created':
          aValue = new Date(a.createdAt || 0).getTime();
          bValue = new Date(b.createdAt || 0).getTime();
          break;
        default:
          aValue = a.code || '';
          bValue = b.code || '';
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        return sortOrder === 'asc'
          ? (aValue as number) - (bValue as number)
          : (bValue as number) - (aValue as number);
      }
    });

    return sorted;
  }, [lockers, sortBy, sortOrder]);

  const deleteLockerMutation = useMutation({
    mutationFn: async (lockerId: number) => {
      const response = await apiRequest("DELETE", `/api/lockers/${lockerId}`);
      return response.json();
    },
    onSuccess: (data: any) => {
      toast({
        title: "Locker Deleted",
        description: data.message || "The locker has been successfully deleted.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/lockers"] });
      queryClient.invalidateQueries({ queryKey: ["/api/stats"] });
      setShowDeleteDialog(false);
      setSelectedLocker(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete locker",
        variant: "destructive",
      });
    },
  });

  // Real-time updates via WebSocket
  useEffect(() => {
    if (lastMessage) {
      const messageTypes = [
        'locker_status_updated',
        'esp32_command',
        'booking_created',
        'booking_updated',
        'locker_created'
      ];

      if (messageTypes.includes(lastMessage.type)) {
        queryClient.invalidateQueries({ queryKey: ["/api/lockers"] });
      }
    }
  }, [lastMessage]);

  const handleDeleteLocker = (locker: Locker) => {
    setSelectedLocker(locker);
    setShowDeleteDialog(true);
  };

  const handleDeleteSubmit = () => {
    if (selectedLocker) {
      deleteLockerMutation.mutate(selectedLocker.id);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-50 border-green-500 text-green-800";
      case "occupied":
        return "bg-red-50 border-red-500 text-red-800";
      case "maintenance":
        return "bg-blue-50 border-blue-500 text-blue-800";
      case "expired":
        return "bg-orange-50 border-orange-500 text-orange-800";
      default:
        return "bg-gray-50 border-gray-500 text-gray-800";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "available":
        return "Available";
      case "occupied":
        return "Occupied";
      case "maintenance":
        return "Maintenance";
      case "expired":
        return "Expired";
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Lockers Management</h2>
          <p className="text-gray-600">Monitor and manage all locker units</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => setAddLockerOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Locker
        </Button>
      </div>

      {/* Sorting Controls */}
      <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          <ArrowUpDown className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">Sort by:</span>
        </div>
        <Select value={sortBy} onValueChange={(value: 'code' | 'status' | 'size' | 'created') => setSortBy(value)}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="code">Code (A-Z)</SelectItem>
            <SelectItem value="status">Status</SelectItem>
            <SelectItem value="size">Size</SelectItem>
            <SelectItem value="created">Date Created</SelectItem>
          </SelectContent>
        </Select>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          className="flex items-center gap-2"
        >
          <ArrowUpDown className="w-3 h-3" />
          {sortOrder === 'asc' ? 'Ascending' : 'Descending'}
        </Button>
        <div className="ml-auto text-sm text-gray-500">
          {sortedLockers.length} locker{sortedLockers.length !== 1 ? 's' : ''} total
        </div>
      </div>

      {/* Add Locker Dialog */}
      <AddLockerDialog open={addLockerOpen} onOpenChange={setAddLockerOpen} />

      {/* Pagination Controls */}
      {sortedLockers.length > lockersPerPage && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage > 1) setCurrentPage(currentPage - 1);
                }}
                className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>

            {Array.from({ length: Math.ceil(sortedLockers.length / lockersPerPage) }, (_, i) => i + 1)
              .slice(Math.max(0, currentPage - 3), Math.min(Math.ceil(sortedLockers.length / lockersPerPage), currentPage + 2))
              .map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      setCurrentPage(page);
                    }}
                    isActive={currentPage === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}

            {currentPage < Math.ceil(sortedLockers.length / lockersPerPage) - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage < Math.ceil(sortedLockers.length / lockersPerPage)) {
                    setCurrentPage(currentPage + 1);
                  }
                }}
                className={currentPage === Math.ceil(sortedLockers.length / lockersPerPage) ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}

      {/* Lockers Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        {sortedLockers
          .slice((currentPage - 1) * lockersPerPage, currentPage * lockersPerPage)
          .map((locker: Locker) => (
          <Card
            key={locker.id}
            className={cn(
              "border-2 transition-shadow hover:shadow-md cursor-pointer relative",
              getStatusColor(locker.status)
            )}
          >
            <CardContent className="p-4 text-center">
              {/* Delete Button Dropdown */}
              <div className="absolute top-2 right-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="p-1 h-6 w-6">
                      <MoreHorizontal className="w-3 h-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleDeleteLocker(locker)}>
                      <Trash2 className="w-3 h-3 mr-2" />
                      Delete Locker
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <Badge
                variant="secondary"
                className={cn(
                  "mb-2 text-xs font-medium",
                  getStatusColor(locker.status)
                )}
              >
                {getStatusText(locker.status)}
              </Badge>
              <div className="text-2xl font-bold text-gray-900">{locker.code}</div>
              <div className="text-sm text-gray-500 capitalize">{locker.size}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Status Legend */}
      <div className="flex items-center justify-center space-x-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded"></div>
          <span>Available</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded"></div>
          <span>Occupied</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded"></div>
          <span>Maintenance</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-orange-500 rounded"></div>
          <span>Expired</span>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-600">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Delete Locker
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedLocker && (
              <>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-medium text-red-800 mb-2">
                    Are you sure you want to delete this locker?
                  </h4>
                  <div className="text-sm text-red-700 space-y-1">
                    <p><strong>Locker Code:</strong> {selectedLocker.code}</p>
                    <p><strong>Size:</strong> {selectedLocker.size}</p>
                    <p><strong>Status:</strong> {selectedLocker.status}</p>
                  </div>
                </div>

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Warning:</strong> This action cannot be undone. All related data including:
                    <ul className="list-disc list-inside mt-2 text-sm">
                      <li>Historical bookings and activities</li>
                      <li>Access sessions and notifications</li>
                      <li>Associated tasks and records</li>
                    </ul>
                    will be permanently deleted.
                  </AlertDescription>
                </Alert>

                <div className="flex space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowDeleteDialog(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDeleteSubmit}
                    disabled={deleteLockerMutation.isPending}
                    className="flex-1"
                  >
                    {deleteLockerMutation.isPending ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                        Deleting...
                      </>
                    ) : (
                      <>
                        <Trash2 className="w-3 h-3 mr-2" />
                        Delete Locker
                      </>
                    )}
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
