import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useWebSocket } from "@/hooks/useWebSocket";
import { apiRequest, queryClient } from "@/lib/queryClient";
import {
  Package,
  User,
  Truck,
  Clock,
  Calendar,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Timer,
  Eye,
  Edit,
  Trash2
} from "lucide-react";

interface LockerWithDetails {
  id: number;
  code: string;
  size: string;
  status: string;
  booking?: {
    id: number;
    userId: string;
    startTime: number;
    endTime: number;
    duration: number;
    totalCost: number;
    status: string;
    user?: {
      id: string;
      username: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    agent?: {
      id: string;
      username: string;
      firstName: string;
      lastName: string;
    };
    task?: {
      id: number;
      type: string;
      status: string;
      agentId: string;
    };
  };
  accessSession?: {
    id: number;
    sessionEnd: number;
    graceEnd: number;
    status: string;
    warningsSent: number;
  };
}

export function LockerStatusManagement() {
  const [selectedLocker, setSelectedLocker] = useState<LockerWithDetails | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showGracePeriodDialog, setShowGracePeriodDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showExtendBookingDialog, setShowExtendBookingDialog] = useState(false);
  const [gracePeriodMinutes, setGracePeriodMinutes] = useState(20);
  const [extensionMinutes, setExtensionMinutes] = useState(60);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  
  const { toast } = useToast();
  const { lastMessage } = useWebSocket();

  // Fetch lockers with detailed information
  const { data: lockers = [], isLoading } = useQuery<LockerWithDetails[]>({
    queryKey: ["/api/admin/lockers/detailed"],
    refetchInterval: 5000,
  });

  // Real-time updates
  useEffect(() => {
    if (lastMessage) {
      const messageTypes = [
        'locker_status_updated',
        'booking_created',
        'booking_updated',
        'task_update',
        'locker_expiration_notification'
      ];
      
      if (messageTypes.includes(lastMessage.type)) {
        queryClient.invalidateQueries({ queryKey: ["/api/admin/lockers/detailed"] });
      }
    }
  }, [lastMessage]);

  // Adjust grace period mutation
  const adjustGracePeriodMutation = useMutation({
    mutationFn: async ({ sessionId, gracePeriodMinutes }: { sessionId: number; gracePeriodMinutes: number }) => {
      return apiRequest("POST", `/api/admin/sessions/${sessionId}/adjust-grace-period`, {
        gracePeriodMinutes
      });
    },
    onSuccess: () => {
      toast({
        title: "Grace Period Updated",
        description: "The grace period has been successfully adjusted.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/lockers/detailed"] });
      setShowGracePeriodDialog(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to adjust grace period",
        variant: "destructive",
      });
    },
  });

  // Extend booking mutation
  const extendBookingMutation = useMutation({
    mutationFn: async ({ bookingId, extensionMinutes }: { bookingId: number; extensionMinutes: number }) => {
      return apiRequest("POST", `/api/admin/bookings/${bookingId}/extend`, {
        extensionMinutes
      });
    },
    onSuccess: () => {
      toast({
        title: "Booking Extended",
        description: "The booking time has been successfully extended.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/lockers/detailed"] });
      setShowExtendBookingDialog(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to extend booking",
        variant: "destructive",
      });
    },
  });

  // Delete locker mutation
  const deleteLockerMutation = useMutation({
    mutationFn: async (lockerId: number) => {
      const response = await apiRequest("DELETE", `/api/lockers/${lockerId}`);
      return response.json();
    },
    onSuccess: (data: any) => {
      toast({
        title: "Locker Deleted",
        description: data.message || "The locker has been successfully deleted.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/lockers/detailed"] });
      queryClient.invalidateQueries({ queryKey: ["/api/lockers"] });
      queryClient.invalidateQueries({ queryKey: ["/api/stats"] });
      setShowDeleteDialog(false);
      setSelectedLocker(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete locker",
        variant: "destructive",
      });
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available": return "bg-green-100 text-green-800 border-green-200";
      case "occupied": return "bg-blue-100 text-blue-800 border-blue-200";
      case "expired": return "bg-orange-100 text-orange-800 border-orange-200";
      case "maintenance": return "bg-gray-100 text-gray-800 border-gray-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusDisplay = (locker: LockerWithDetails) => {
    const now = Date.now();
    const hasActiveBooking = locker.booking && (locker.booking.status === 'active' || locker.booking.status === 'confirmed');
    const isExpired = hasActiveBooking && now > locker.booking.endTime;

    if (locker.status === "expired" && hasActiveBooking) {
      return {
        status: "expired",
        label: "Expired (Items Present)",
        description: "Booking time has ended but items may still be in locker"
      };
    }

    return {
      status: locker.status,
      label: locker.status,
      description: null
    };
  };

  const getSessionStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "expired": return "bg-red-100 text-red-800";
      case "grace_period": return "bg-yellow-100 text-yellow-800";
      case "revoked": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const formatDateTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) return `${hours}h`;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getRemainingTime = (endTime: number) => {
    const now = Date.now();
    const remaining = endTime - now;
    if (remaining <= 0) return "Expired";
    
    const hours = Math.floor(remaining / (1000 * 60 * 60));
    const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const filteredLockers = lockers.filter(locker => {
    const matchesSearch = locker.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         locker.booking?.user?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         locker.booking?.user?.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         locker.booking?.user?.lastName?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || locker.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleViewDetails = (locker: LockerWithDetails) => {
    setSelectedLocker(locker);
    setShowDetails(true);
  };

  const handleAdjustGracePeriod = (locker: LockerWithDetails) => {
    setSelectedLocker(locker);
    setGracePeriodMinutes(20); // Default value
    setShowGracePeriodDialog(true);
  };

  const handleExtendBooking = (locker: LockerWithDetails) => {
    setSelectedLocker(locker);
    setExtensionMinutes(60); // Default 1 hour extension
    setShowExtendBookingDialog(true);
  };

  const handleDeleteLocker = (locker: LockerWithDetails) => {
    setSelectedLocker(locker);
    setShowDeleteDialog(true);
  };

  const handleGracePeriodSubmit = () => {
    if (selectedLocker?.accessSession) {
      adjustGracePeriodMutation.mutate({
        sessionId: selectedLocker.accessSession.id,
        gracePeriodMinutes
      });
    }
  };

  const handleExtendBookingSubmit = () => {
    if (selectedLocker?.booking) {
      extendBookingMutation.mutate({
        bookingId: selectedLocker.booking.id,
        extensionMinutes
      });
    }
  };

  const handleDeleteSubmit = () => {
    if (selectedLocker) {
      deleteLockerMutation.mutate(selectedLocker.id);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading locker status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Locker Status Management</h1>
          <p className="text-gray-600">Monitor locker usage, users, agents, and manage grace periods</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex space-x-4 items-center">
        <div className="flex-1 max-w-md">
          <Label htmlFor="search">Search Lockers</Label>
          <Input
            id="search"
            placeholder="Search by locker code, user name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="status-filter">Filter by Status</Label>
          <select
            id="status-filter"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="available">Available</option>
            <option value="occupied">Occupied</option>
            <option value="expired">Expired</option>
            <option value="maintenance">Maintenance</option>
          </select>
        </div>
      </div>

      {/* Lockers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredLockers.map((locker) => (
          <Card key={locker.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Locker {locker.code}</CardTitle>
                <div className="text-right">
                  <Badge className={getStatusColor(getStatusDisplay(locker).status)}>
                    {getStatusDisplay(locker).label}
                  </Badge>
                  {getStatusDisplay(locker).description && (
                    <p className="text-xs text-gray-500 mt-1">
                      {getStatusDisplay(locker).description}
                    </p>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-500">Size: {locker.size}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              {locker.booking ? (
                <>
                  {/* User Information */}
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-blue-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        {locker.booking.user?.firstName} {locker.booking.user?.lastName}
                      </p>
                      <p className="text-xs text-gray-500">@{locker.booking.user?.username}</p>
                    </div>
                  </div>

                  {/* Agent Information */}
                  {locker.booking.agent && (
                    <div className="flex items-center space-x-2">
                      <Truck className="w-4 h-4 text-purple-600" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          Agent: {locker.booking.agent.firstName} {locker.booking.agent.lastName}
                        </p>
                        <p className="text-xs text-gray-500">@{locker.booking.agent.username}</p>
                      </div>
                    </div>
                  )}

                  {/* Rental Duration */}
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-green-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        Duration: {formatDuration(locker.booking.duration)}
                      </p>
                      <p className="text-xs text-gray-500">
                        Remaining: {getRemainingTime(locker.booking.endTime)}
                      </p>
                    </div>
                  </div>

                  {/* Access Session Status */}
                  {locker.accessSession && (
                    <div className="flex items-center space-x-2">
                      <Timer className="w-4 h-4 text-orange-600" />
                      <div className="flex-1">
                        <Badge className={getSessionStatusColor(locker.accessSession.status)}>
                          {locker.accessSession.status}
                        </Badge>
                        {locker.accessSession.status === "grace_period" && (
                          <p className="text-xs text-gray-500 mt-1">
                            Grace ends: {getRemainingTime(locker.accessSession.graceEnd)}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex space-x-2 pt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleViewDetails(locker)}
                      className="flex-1"
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      Details
                    </Button>
                    {locker.accessSession ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleAdjustGracePeriod(locker)}
                        className="flex-1"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        Grace Period
                      </Button>
                    ) : (
                      // Show extend booking button for expired bookings without access sessions
                      locker.booking &&
                      (locker.booking.status === 'active' || locker.booking.status === 'confirmed') &&
                      locker.booking.endTime &&
                      Date.now() > locker.booking.endTime && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleExtendBooking(locker)}
                          className="flex-1"
                        >
                          <Timer className="w-3 h-3 mr-1" />
                          Extend Time
                        </Button>
                      )
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteLocker(locker)}
                      className="flex-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                      disabled={locker.booking && (locker.booking.status === 'active' || locker.booking.status === 'confirmed')}
                    >
                      <Trash2 className="w-3 h-3 mr-1" />
                      Delete
                    </Button>
                  </div>
                </>
              ) : (
                <div className="text-center py-4">
                  <Package className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No active booking</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredLockers.length === 0 && (
        <div className="text-center py-12">
          <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No lockers found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
        </div>
      )}

      {/* Locker Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Locker {selectedLocker?.code} - Detailed Information</DialogTitle>
          </DialogHeader>
          {selectedLocker && (
            <div className="space-y-6">
              {/* Locker Basic Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Package className="w-5 h-5 mr-2" />
                    Locker Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Code</Label>
                      <p className="text-sm">{selectedLocker.code}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Size</Label>
                      <p className="text-sm">{selectedLocker.size}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Status</Label>
                      <div>
                        <Badge className={getStatusColor(getStatusDisplay(selectedLocker).status)}>
                          {getStatusDisplay(selectedLocker).label}
                        </Badge>
                        {getStatusDisplay(selectedLocker).description && (
                          <p className="text-xs text-gray-500 mt-1">
                            {getStatusDisplay(selectedLocker).description}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Booking Information */}
              {selectedLocker.booking && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Calendar className="w-5 h-5 mr-2" />
                      Booking Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Booking ID</Label>
                        <p className="text-sm">#{selectedLocker.booking.id}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Status</Label>
                        <Badge>{selectedLocker.booking.status}</Badge>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Start Time</Label>
                        <p className="text-sm">{formatDateTime(selectedLocker.booking.startTime)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">End Time</Label>
                        <p className="text-sm">{formatDateTime(selectedLocker.booking.endTime)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Duration</Label>
                        <p className="text-sm">{formatDuration(selectedLocker.booking.duration)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Total Cost</Label>
                        <p className="text-sm">${selectedLocker.booking.totalCost.toFixed(2)}</p>
                      </div>
                    </div>

                    {/* User Details */}
                    {selectedLocker.booking.user && (
                      <div className="border-t pt-4">
                        <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                          <User className="w-4 h-4 mr-2" />
                          Renter Information
                        </h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Name</Label>
                            <p className="text-sm">
                              {selectedLocker.booking.user.firstName} {selectedLocker.booking.user.lastName}
                            </p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Username</Label>
                            <p className="text-sm">@{selectedLocker.booking.user.username}</p>
                          </div>
                          <div className="col-span-2">
                            <Label className="text-sm font-medium text-gray-600">Email</Label>
                            <p className="text-sm">{selectedLocker.booking.user.email}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Agent Details */}
                    {selectedLocker.booking.agent && (
                      <div className="border-t pt-4">
                        <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                          <Truck className="w-4 h-4 mr-2" />
                          Assigned Agent
                        </h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Name</Label>
                            <p className="text-sm">
                              {selectedLocker.booking.agent.firstName} {selectedLocker.booking.agent.lastName}
                            </p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Username</Label>
                            <p className="text-sm">@{selectedLocker.booking.agent.username}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Task Information */}
                    {selectedLocker.booking.task && (
                      <div className="border-t pt-4">
                        <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Delivery Task
                        </h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Task ID</Label>
                            <p className="text-sm">#{selectedLocker.booking.task.id}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Type</Label>
                            <p className="text-sm">{selectedLocker.booking.task.type}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Status</Label>
                            <Badge>{selectedLocker.booking.task.status}</Badge>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Access Session Information */}
              {selectedLocker.accessSession && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Timer className="w-5 h-5 mr-2" />
                      Access Session & Grace Period
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Session Status</Label>
                        <Badge className={getSessionStatusColor(selectedLocker.accessSession.status)}>
                          {selectedLocker.accessSession.status}
                        </Badge>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Warnings Sent</Label>
                        <p className="text-sm">{selectedLocker.accessSession.warningsSent}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Session End</Label>
                        <p className="text-sm">{formatDateTime(selectedLocker.accessSession.sessionEnd)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Grace Period End</Label>
                        <p className="text-sm">{formatDateTime(selectedLocker.accessSession.graceEnd)}</p>
                      </div>
                    </div>

                    {selectedLocker.accessSession.status === "grace_period" && (
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          This session is currently in grace period.
                          Time remaining: {getRemainingTime(selectedLocker.accessSession.graceEnd)}
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Grace Period Adjustment Dialog */}
      <Dialog open={showGracePeriodDialog} onOpenChange={setShowGracePeriodDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Adjust Grace Period</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="grace-period">Grace Period (minutes)</Label>
              <Input
                id="grace-period"
                type="number"
                min="0"
                max="120"
                value={gracePeriodMinutes}
                onChange={(e) => setGracePeriodMinutes(parseInt(e.target.value) || 0)}
                placeholder="Enter minutes"
              />
              <p className="text-sm text-gray-500 mt-1">
                Current grace period will be extended or reduced to this duration.
              </p>
            </div>

            {selectedLocker?.accessSession && (
              <Alert>
                <Timer className="h-4 w-4" />
                <AlertDescription>
                  Current status: {selectedLocker.accessSession.status}<br />
                  Current grace end: {formatDateTime(selectedLocker.accessSession.graceEnd)}
                </AlertDescription>
              </Alert>
            )}

            <div className="flex space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowGracePeriodDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleGracePeriodSubmit}
                disabled={adjustGracePeriodMutation.isPending}
                className="flex-1"
              >
                {adjustGracePeriodMutation.isPending ? "Updating..." : "Update"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Extend Booking Dialog */}
      <Dialog open={showExtendBookingDialog} onOpenChange={setShowExtendBookingDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Extend Booking Time</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="extension-time">Extension Time (minutes)</Label>
              <Input
                id="extension-time"
                type="number"
                min="15"
                max="1440"
                value={extensionMinutes}
                onChange={(e) => setExtensionMinutes(parseInt(e.target.value) || 0)}
                placeholder="Enter minutes"
              />
              <p className="text-sm text-gray-500 mt-1">
                Extend the booking end time by this duration (15 minutes to 24 hours).
              </p>
            </div>

            {selectedLocker?.booking && (
              <Alert>
                <Timer className="h-4 w-4" />
                <AlertDescription>
                  Current end time: {formatDateTime(selectedLocker.booking.endTime)}<br />
                  New end time: {formatDateTime(selectedLocker.booking.endTime + (extensionMinutes * 60 * 1000))}
                </AlertDescription>
              </Alert>
            )}

            <div className="flex space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowExtendBookingDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleExtendBookingSubmit}
                disabled={extendBookingMutation.isPending}
                className="flex-1"
              >
                {extendBookingMutation.isPending ? "Extending..." : "Extend"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-600">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Delete Locker
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedLocker && (
              <>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-medium text-red-800 mb-2">
                    Are you sure you want to delete this locker?
                  </h4>
                  <div className="text-sm text-red-700 space-y-1">
                    <p><strong>Locker Code:</strong> {selectedLocker.code}</p>
                    <p><strong>Size:</strong> {selectedLocker.size}</p>
                    <p><strong>Status:</strong> {selectedLocker.status}</p>
                  </div>
                </div>

                {selectedLocker.booking && (selectedLocker.booking.status === 'active' || selectedLocker.booking.status === 'confirmed') ? (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      This locker cannot be deleted because it has active bookings.
                      Please wait for the booking to complete or cancel it first.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Warning:</strong> This action cannot be undone. All related data including:
                      <ul className="list-disc list-inside mt-2 text-sm">
                        <li>Historical bookings and activities</li>
                        <li>Access sessions and notifications</li>
                        <li>Associated tasks and records</li>
                      </ul>
                      will be permanently deleted.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowDeleteDialog(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDeleteSubmit}
                    disabled={
                      deleteLockerMutation.isPending ||
                      (selectedLocker.booking && (selectedLocker.booking.status === 'active' || selectedLocker.booking.status === 'confirmed'))
                    }
                    className="flex-1"
                  >
                    {deleteLockerMutation.isPending ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                        Deleting...
                      </>
                    ) : (
                      <>
                        <Trash2 className="w-3 h-3 mr-2" />
                        Delete Locker
                      </>
                    )}
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
